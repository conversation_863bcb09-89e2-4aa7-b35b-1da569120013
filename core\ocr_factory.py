"""
OCR工厂类

使用工厂模式创建不同类型的OCR实例，支持依赖注入
"""

from typing import Dict, Any, Optional
from config import LLMConfigManager, AzureConfigManager
from proxy import ProxyManager
from .base_ocr import BaseOCR


class OCRFactory:
    """OCR工厂类，负责创建不同类型的OCR实例"""
    
    def __init__(self):
        self.llm_config_manager = LLMConfigManager()
        self.azure_config_manager = AzureConfigManager()
        self.proxy_manager = ProxyManager()
    
    def create_marker_ocr(self, proxy_type: str = "http") -> BaseOCR:
        """
        创建Marker OCR实例
        
        Args:
            proxy_type: 代理类型，"http" 或 "socks5"
            
        Returns:
            Marker OCR实例
        """
        # 获取LLM配置
        config = self.llm_config_manager.get_active_config()
        
        # 设置代理（仅Gemini需要）
        proxy_url = self.llm_config_manager.get_proxy_url()
        if proxy_url:
            if proxy_type == "http":
                # 使用HTTP代理（转换SOCKS5）
                self.proxy_manager.set_global_proxy_with_conversion(proxy_url)
            else:
                # 使用SOCKS5代理
                self.proxy_manager.set_global_proxy(proxy_url)
        
        # 根据配置创建相应的OCR实例
        if self.llm_config_manager.should_use_gemini():
            if proxy_type == "http":
                try:
                    from services.marker_http_ocr import MarkerHTTPOCR
                    return MarkerHTTPOCR(config)
                except ImportError as e:
                    print(f"⚠️ 无法导入MarkerHTTPOCR: {e}")
                    raise
            else:
                try:
                    from services.marker_socks5_ocr import MarkerSOCKS5OCR
                    return MarkerSOCKS5OCR(config)
                except ImportError as e:
                    print(f"⚠️ 无法导入MarkerSOCKS5OCR: {e}")
                    raise
        else:
            try:
                from services.marker_openrouter_ocr import MarkerOpenRouterOCR
                return MarkerOpenRouterOCR(config)
            except ImportError as e:
                print(f"⚠️ 无法导入MarkerOpenRouterOCR: {e}")
                raise
    
    def create_azure_ocr(self) -> Optional[BaseOCR]:
        """
        创建Azure OCR实例
        
        Returns:
            Azure OCR实例，如果配置无效则返回None
        """
        config = self.azure_config_manager.create_azure_config()
        if not config:
            return None
        
        try:
            from services.azure_ocr_service import AzureOCRService
            return AzureOCRService(config.to_dict())
        except ImportError as e:
            print(f"⚠️ 无法导入AzureOCRService: {e}")
            raise
    
    def get_default_ocr(self) -> BaseOCR:
        """
        获取默认OCR实例（HTTP代理 + Gemini）
        
        Returns:
            默认OCR实例
        """
        return self.create_marker_ocr(proxy_type="http")
    
    def cleanup_proxy(self):
        """清理代理设置"""
        self.proxy_manager.clear_proxy()
    
    def validate_configurations(self) -> Dict[str, bool]:
        """
        验证所有配置
        
        Returns:
            配置验证结果字典
        """
        return {
            "llm_config": self.llm_config_manager.validate_config(),
            "azure_config": self.azure_config_manager.validate_config()
        }
