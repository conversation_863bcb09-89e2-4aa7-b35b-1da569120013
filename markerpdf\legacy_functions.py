"""
向后兼容性函数

保留原有的函数接口，内部使用新的架构实现
"""

import os
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

from config import LLMConfigManager
from core import OCRFactory


def create_gemini_config() -> Dict[str, Any]:
    """
    创建Gemini配置（向后兼容）
    
    Returns:
        Gemini配置字典
    """
    print("⚠️ 使用向后兼容的create_gemini_config函数")
    print("💡 建议使用新的config.LLMConfigManager.create_gemini_config()")
    
    config_manager = LLMConfigManager()
    gemini_config = config_manager.create_gemini_config()
    return gemini_config.to_dict()


def create_openrouter_config() -> Dict[str, Any]:
    """
    创建OpenRouter配置（向后兼容）
    
    Returns:
        OpenRouter配置字典
    """
    print("⚠️ 使用向后兼容的create_openrouter_config函数")
    print("💡 建议使用新的config.LLMConfigManager.create_openrouter_config()")
    
    config_manager = LLMConfigManager()
    openrouter_config = config_manager.create_openrouter_config()
    return openrouter_config.to_dict()


def test_model(config: Dict[str, Any], model_name: str, pdf_path: str = "sample.pdf") -> Dict[str, Any]:
    """
    测试模型（向后兼容）
    
    Args:
        config: 模型配置
        model_name: 模型名称
        pdf_path: PDF文件路径
        
    Returns:
        测试结果字典
    """
    print(f"⚠️ 使用向后兼容的test_model函数测试 {model_name}")
    print("💡 建议使用新的core.OCRFactory创建OCR实例")
    
    try:
        # 使用新架构
        factory = OCRFactory()
        
        # 根据配置判断使用哪种OCR
        if "gemini" in model_name.lower():
            ocr = factory.create_marker_ocr(proxy_type="http")  # 默认使用HTTP代理
        elif "openrouter" in model_name.lower() or "deepseek" in model_name.lower():
            ocr = factory.create_marker_ocr(proxy_type="socks5")  # OpenRouter不需要代理
        else:
            # 默认使用HTTP代理
            ocr = factory.create_marker_ocr(proxy_type="http")
        
        # 处理文档
        result = ocr.process_document(pdf_path)
        
        # 转换为旧格式
        return {
            'model_name': result.model_name,
            'success': result.success,
            'error': result.error,
            'processing_time': result.processing_time,
            'text_length': result.text_length,
            'text_content': result.text_content,
            'output_folder': result.output_folder,
            'config': result.config,
            'attempts': result.attempts,
            'image_count': result.image_count,
            'images': result.images
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return {
            'model_name': model_name,
            'success': False,
            'error': str(e),
            'processing_time': 0,
            'text_length': 0,
            'text_content': '',
            'output_folder': None,
            'config': config,
            'attempts': 1,
            'image_count': 0,
            'images': {}
        }


def save_results_to_markdown(results: List[Dict[str, Any]]) -> str:
    """
    保存结果到Markdown文件（向后兼容）
    
    Args:
        results: 结果列表
        
    Returns:
        报告文件路径
    """
    print("⚠️ 使用向后兼容的save_results_to_markdown函数")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"ocr_comparison_results/comparison_report_{timestamp}.md"
    
    # 确保目录存在
    Path(report_file).parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# OCR模型比较报告\n\n")
        f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 写入总结
        f.write("## 测试总结\n\n")
        f.write("| 模型 | 状态 | 处理时间(秒) | 文本长度 | 尝试次数 |\n")
        f.write("|------|------|-------------|----------|----------|\n")
        
        for result in results:
            status = "✅ 成功" if result['success'] else "❌ 失败"
            f.write(f"| {result['model_name']} | {status} | {result['processing_time']:.2f} | {result['text_length']} | {result['attempts']} |\n")
        
        f.write("\n")
        
        # 写入详细结果
        for i, result in enumerate(results, 1):
            f.write(f"## {i}. {result['model_name']}\n\n")
            
            if result['success']:
                f.write(f"**状态**: ✅ 成功\n")
                f.write(f"**处理时间**: {result['processing_time']:.2f}秒\n")
                f.write(f"**文本长度**: {result['text_length']}字符\n")
                f.write(f"**尝试次数**: {result['attempts']}\n")
                f.write(f"**输出目录**: {result['output_folder']}\n\n")
                
                if result['text_content']:
                    f.write("### 提取的文本内容\n\n")
                    f.write("```\n")
                    f.write(result['text_content'][:1000])  # 限制长度
                    if len(result['text_content']) > 1000:
                        f.write("\n... (内容已截断，完整内容请查看输出文件)")
                    f.write("\n```\n\n")
            else:
                f.write(f"**状态**: ❌ 失败\n")
                f.write(f"**错误信息**: {result['error']}\n")
                f.write(f"**尝试次数**: {result['attempts']}\n\n")
    
    print(f"✅ 比较报告已保存: {report_file}")
    return report_file


# 保留一些常用的辅助函数
def save_model_output(result: Dict[str, Any], model_name: str) -> str:
    """保存模型输出（向后兼容）"""
    print("⚠️ 使用向后兼容的save_model_output函数")
    
    if result.get('output_folder'):
        return result['output_folder']
    
    # 如果没有输出文件夹，创建一个
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    folder_name = f"output_{model_name.replace(' ', '_')}_{timestamp}"
    output_folder = Path(".") / folder_name
    output_folder.mkdir(parents=True, exist_ok=True)
    
    # 保存文本内容
    text_file = output_folder / "extracted_text.md"
    with open(text_file, 'w', encoding='utf-8') as f:
        f.write(f"# OCR结果 - {model_name}\n\n")
        f.write(result.get('text_content', ''))
    
    return str(output_folder)
