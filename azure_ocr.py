import asyncio
import os
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

# Import Document Intelligence async client and required modules
from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence.aio import DocumentIntelligenceClient
from azure.ai.documentintelligence.models import (
    AnalyzeResult,
    DocumentAnalysisFeature,
    DocumentContentFormat
)

# Import environment variable management
from dotenv import load_dotenv
load_dotenv()

def disable_proxy_for_azure():
    """Disable proxy settings for Azure API calls"""
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    original_proxies = {}

    for var in proxy_vars:
        if var in os.environ:
            original_proxies[var] = os.environ[var]
            del os.environ[var]

    return original_proxies

def restore_proxy_settings(original_proxies):
    """Restore original proxy settings"""
    for var, value in original_proxies.items():
        os.environ[var] = value

def create_azure_config():
    """Create Azure Document Intelligence configuration"""
    print(f"\n{'='*50}")
    print("Configuring Azure Document Intelligence Service")
    print(f"{'='*50}")
    
    config = {
        "azure_endpoint": os.getenv('AZURE_DOCINTEL_ENDPOINT'),
        "azure_key": os.getenv('AZURE_DOCINTEL_KEY'),
        "model_id": os.getenv('AZURE_MODEL_ID', 'prebuilt-layout'),
        "locale": os.getenv('AZURE_LOCALE', 'en-US'),
        "features": [
            DocumentAnalysisFeature.FORMULAS,
            DocumentAnalysisFeature.OCR_HIGH_RESOLUTION
        ],
        "output_format": DocumentContentFormat.MARKDOWN
    }
    
    # Validate required configuration
    if not config["azure_endpoint"] or not config["azure_key"]:
        print("⚠️ Warning: Azure Document Intelligence endpoint or key not set")
        print("Please set AZURE_DOCINTEL_ENDPOINT and AZURE_DOCINTEL_KEY in .env file")
        return None
    
    print(f"✅ Azure endpoint: {config['azure_endpoint']}")
    print(f"✅ Model ID: {config['model_id']}")
    print(f"✅ Locale: {config['locale']}")
    
    return config

async def analyze_single_document(
    client: DocumentIntelligenceClient, 
    pdf_path: str, 
    doc_locale: str,
    model_id: str = "prebuilt-layout",
    features: Optional[List[DocumentAnalysisFeature]] = None
) -> Dict[str, Any]:
    """
    Asynchronously analyze a single PDF file and optimize configuration based on document language.
    
    Args:
        client: Async DocumentIntelligenceClient instance.
        pdf_path: Local path to the PDF file.
        doc_locale: Document locale, e.g., "en-US" or "zh-Hant".
        model_id: Model ID to use.
        features: List of analysis features.
    
    Returns:
        Dictionary containing analysis results.
    """
    print(f"🚀 Starting file analysis: {os.path.basename(pdf_path)} (locale: {doc_locale})")
    
    if not os.path.exists(pdf_path):
        error_msg = f"❌ File does not exist: {pdf_path}"
        print(error_msg)
        return {
            'success': False,
            'error': error_msg,
            'file_path': pdf_path,
            'content': None,
            'processing_time': 0
        }
    
    # Set default features
    if features is None:
        features = [
            DocumentAnalysisFeature.FORMULAS,
            DocumentAnalysisFeature.OCR_HIGH_RESOLUTION
        ]
    
    start_time = time.time()
    
    try:
        with open(pdf_path, "rb") as f:
            # Core: Initiate analysis request
            # Added locale parameter to improve language-specific recognition accuracy
            poller = await client.begin_analyze_document(
                model_id=model_id,
                body=f,
                locale=doc_locale,
                output_content_format=DocumentContentFormat.MARKDOWN
            )
        
        result: AnalyzeResult = await poller.result()
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"✅ File analysis completed: {os.path.basename(pdf_path)}")
        print(f"⏱️ Processing time: {processing_time:.2f} seconds")
        
        if result.content:
            print(f"📄 Extracted content length: {len(result.content)} characters")
            return {
                'success': True,
                'error': None,
                'file_path': pdf_path,
                'content': result.content,
                'processing_time': processing_time,
                'locale': doc_locale,
                'model_id': model_id,
                'content_length': len(result.content)
            }
        else:
            warning_msg = f"⚠️ File {os.path.basename(pdf_path)} returned no content."
            print(warning_msg)
            return {
                'success': False,
                'error': warning_msg,
                'file_path': pdf_path,
                'content': None,
                'processing_time': processing_time
            }
            
    except Exception as e:
        end_time = time.time()
        processing_time = end_time - start_time
        error_msg = f"❌ Error analyzing file {os.path.basename(pdf_path)}: {e}"
        print(error_msg)
        return {
            'success': False,
            'error': error_msg,
            'file_path': pdf_path,
            'content': None,
            'processing_time': processing_time
        }

def save_azure_output(result: Dict[str, Any], model_name: str = "Azure Document AI", 
                     base_dir: str = "ocr_comparison_results") -> Optional[Path]:
    """Save Azure Document AI output results"""
    if not result['success'] or not result['content']:
        print(f"⚠️ Unable to save results: {result.get('error', 'Unknown error')}")
        return None
    
    # Create model-specific folder
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    model_folder = Path(base_dir) / f"{model_name.replace(' ', '_').replace('/', '_')}_{timestamp}"
    model_folder.mkdir(parents=True, exist_ok=True)
    
    # Save markdown file
    markdown_file = model_folder / "ocr_result.md"
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(f"# {model_name} OCR Results\n\n")
        f.write(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Add configuration information
        f.write("## Configuration Parameters\n\n")
        f.write(f"- **Processing Time**: {result['processing_time']:.2f} seconds\n")
        f.write(f"- **Locale**: {result.get('locale', 'N/A')}\n")
        f.write(f"- **Model ID**: {result.get('model_id', 'N/A')}\n")
        f.write(f"- **Content Length**: {result.get('content_length', 0)} characters\n\n")
        
        f.write("## Extracted Text Content\n\n")
        f.write(result['content'])
    
    print(f"  Azure model output saved to: {model_folder}")
    return model_folder

async def test_azure_ocr(pdf_path: str = "sample.pdf", doc_locale: str = "en-US") -> Dict[str, Any]:
    """Test Azure Document Intelligence OCR performance"""
    print(f"\n{'='*50}")
    print("Starting Azure Document Intelligence Test")
    print(f"{'='*50}")

    # Get configuration
    config = create_azure_config()
    if not config:
        return {
            'model_name': 'Azure Document AI',
            'success': False,
            'error': 'Incomplete Azure configuration',
            'processing_time': 0,
            'text_length': 0,
            'text_content': '',
            'output_folder': None
        }

    # Disable proxy for Azure API calls
    print("🔧 Disabling proxy settings for Azure API...")
    original_proxies = disable_proxy_for_azure()

    try:
        # Create async client
        async with DocumentIntelligenceClient(
            endpoint=config["azure_endpoint"],
            credential=AzureKeyCredential(config["azure_key"])
        ) as client:

            # Analyze document
            result = await analyze_single_document(
                client=client,
                pdf_path=pdf_path,
                doc_locale=doc_locale,
                model_id=config["model_id"],
                features=config["features"]
            )
            
            if result['success']:
                # Save results
                output_folder = save_azure_output(result, "Azure Document AI")
                
                return {
                    'model_name': 'Azure Document AI',
                    'success': True,
                    'error': None,
                    'processing_time': result['processing_time'],
                    'text_length': len(result['content']),
                    'image_count': 0,  # Azure Document AI doesn't directly provide image extraction
                    'text_content': result['content'],
                    'images': {},
                    'output_folder': output_folder,
                    'config': config,
                    'attempts': 1
                }
            else:
                return {
                    'model_name': 'Azure Document AI',
                    'success': False,
                    'error': result['error'],
                    'processing_time': result['processing_time'],
                    'text_length': 0,
                    'image_count': 0,
                    'text_content': '',
                    'images': {},
                    'output_folder': None,
                    'config': config,
                    'attempts': 1
                }

    except Exception as e:
        error_msg = f"Azure Document AI test failed: {e}"
        print(f"❌ {error_msg}")
        return {
            'model_name': 'Azure Document AI',
            'success': False,
            'error': error_msg,
            'processing_time': 0,
            'text_length': 0,
            'image_count': 0,
            'text_content': '',
            'images': {},
            'output_folder': None,
            'config': config if 'config' in locals() else {},
            'attempts': 1
        }
    finally:
        # Restore original proxy settings
        print("🔧 Restoring original proxy settings...")
        restore_proxy_settings(original_proxies)

# Function for batch processing
async def batch_analyze_documents(documents_list: List[tuple]) -> List[Dict[str, Any]]:
    """
    Batch analyze multiple documents

    Args:
        documents_list: List of tuples, each containing (file_path, locale)

    Returns:
        List of analysis results
    """
    config = create_azure_config()
    if not config:
        return []

    # Disable proxy for Azure API calls
    print("🔧 Disabling proxy settings for batch processing...")
    original_proxies = disable_proxy_for_azure()

    try:
        async with DocumentIntelligenceClient(
            endpoint=config["azure_endpoint"],
            credential=AzureKeyCredential(config["azure_key"])
        ) as client:
            tasks = [
                analyze_single_document(
                    client, pdf_path, locale,
                    config["model_id"], config["features"]
                )
                for pdf_path, locale in documents_list
            ]
            results = await asyncio.gather(*tasks)

        print("\n🎉 All files processing completed.")
        return results
    finally:
        # Restore original proxy settings
        print("🔧 Restoring original proxy settings...")
        restore_proxy_settings(original_proxies)

if __name__ == "__main__":
    # Test single document
    result = asyncio.run(test_azure_ocr())
    print(f"\nTest result: {result}")
