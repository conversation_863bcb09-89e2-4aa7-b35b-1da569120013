"""
Azure Document Intelligence OCR服务实现

使用Azure Document Intelligence的OCR服务
"""

import os
import asyncio
import time
from typing import Dict, Any

from azure.ai.documentintelligence.aio import DocumentIntelligenceClient
from azure.core.credentials import AzureKeyCredential

from core.base_ocr import BaseOCR, OCRResult


class AzureOCRService(BaseOCR):
    """Azure Document Intelligence OCR服务"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model_name = "Azure Document AI"
    
    def validate_config(self) -> bool:
        """验证Azure配置"""
        required_keys = ['azure_endpoint', 'azure_key']
        return all(key in self.config and self.config[key] for key in required_keys)
    
    def process_document(self, pdf_path: str, doc_locale: str = "en-US", **kwargs) -> OCRResult:
        """
        使用Azure Document Intelligence处理PDF文档
        
        Args:
            pdf_path: PDF文件路径
            doc_locale: 文档语言区域
            **kwargs: 其他参数
            
        Returns:
            OCR处理结果
        """
        print(f"\n{'='*50}")
        print(f"开始 {self.model_name} OCR处理")
        print(f"文件: {pdf_path}")
        print(f"{'='*50}")
        
        if not self.validate_config():
            return OCRResult(
                model_name=self.model_name,
                success=False,
                error="配置验证失败",
                processing_time=0,
                text_length=0,
                text_content="",
                output_folder=None,
                config=self.config,
                attempts=0
            )
        
        # 禁用代理（Azure API不需要代理）
        print("🔧 禁用代理设置（Azure API）...")
        original_proxies = self._disable_proxy_for_azure()
        
        try:
            # 运行异步处理
            result = asyncio.run(self._process_document_async(pdf_path, doc_locale))
            return result
            
        finally:
            # 恢复原始代理设置
            print("🔧 恢复原始代理设置...")
            self._restore_proxy_settings(original_proxies)
    
    async def _process_document_async(self, pdf_path: str, doc_locale: str) -> OCRResult:
        """异步处理文档"""
        try:
            start_time = time.time()
            
            # 创建异步客户端
            async with DocumentIntelligenceClient(
                endpoint=self.config["azure_endpoint"],
                credential=AzureKeyCredential(self.config["azure_key"])
            ) as client:
                
                print("🔄 开始Azure文档分析...")
                
                # 读取PDF文件
                with open(pdf_path, "rb") as f:
                    pdf_content = f.read()
                
                # 开始分析
                poller = await client.begin_analyze_document(
                    model_id=self.config.get("model_id", "prebuilt-layout"),
                    analyze_request=pdf_content,
                    content_type="application/pdf",
                    locale=doc_locale,
                    features=self.config.get("features", ["MATH_SOLVER"])
                )
                
                # 等待结果
                result = await poller.result()
                
                # 提取文本内容
                text_content = ""
                if result.content:
                    text_content = result.content
                
                processing_time = time.time() - start_time
                
                print(f"✅ Azure分析完成！")
                print(f"⏱️ 处理时间: {processing_time:.2f}秒")
                print(f"📄 提取文本长度: {len(text_content)}字符")
                
                # 保存输出
                ocr_result = OCRResult(
                    model_name=self.model_name,
                    success=True,
                    error=None,
                    processing_time=processing_time,
                    text_length=len(text_content),
                    text_content=text_content,
                    output_folder=None,
                    config=self.config,
                    attempts=1,
                    image_count=0,  # Azure不直接提供图像提取
                    images={}
                )
                
                output_folder = self.save_output(ocr_result, ".")
                ocr_result.output_folder = output_folder
                
                return ocr_result
                
        except Exception as e:
            error_msg = str(e)
            print(f"❌ Azure处理失败: {error_msg}")
            
            return OCRResult(
                model_name=self.model_name,
                success=False,
                error=error_msg,
                processing_time=0,
                text_length=0,
                text_content="",
                output_folder=None,
                config=self.config,
                attempts=1
            )
    
    def _disable_proxy_for_azure(self) -> Dict[str, str]:
        """禁用Azure API的代理设置"""
        original_proxies = {}
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
        
        for var in proxy_vars:
            if var in os.environ:
                original_proxies[var] = os.environ[var]
                del os.environ[var]
        
        return original_proxies
    
    def _restore_proxy_settings(self, original_proxies: Dict[str, str]):
        """恢复原始代理设置"""
        for var, value in original_proxies.items():
            os.environ[var] = value
