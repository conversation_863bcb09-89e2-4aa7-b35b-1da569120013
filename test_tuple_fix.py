#!/usr/bin/env python3
"""
测试tuple写入文件错误的修复
"""

import sys
import os
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_to_dict():
    """测试配置类的to_dict方法是否正确处理tuple/list"""
    print("🔍 测试配置类的to_dict方法...")
    
    try:
        from config import LLMConfigManager
        
        config_manager = LLMConfigManager()
        
        # 测试Gemini配置
        if config_manager.should_use_gemini():
            gemini_config = config_manager.create_gemini_config()
            config_dict = gemini_config.to_dict()
            
            print("✅ Gemini配置转换成功")
            print(f"  langs类型: {type(config_dict['langs'])}")
            print(f"  langs值: {config_dict['langs']}")
            
            # 确保langs是字符串
            if isinstance(config_dict['langs'], str):
                print("✅ langs已正确转换为字符串")
            else:
                print(f"❌ langs仍然是 {type(config_dict['langs'])}")
                return False
        
        # 测试OpenRouter配置
        openrouter_config = config_manager.create_openrouter_config()
        config_dict = openrouter_config.to_dict()
        
        print("✅ OpenRouter配置转换成功")
        print(f"  langs类型: {type(config_dict['langs'])}")
        print(f"  langs值: {config_dict['langs']}")
        
        # 确保langs是字符串
        if isinstance(config_dict['langs'], str):
            print("✅ langs已正确转换为字符串")
        else:
            print(f"❌ langs仍然是 {type(config_dict['langs'])}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ocr_result_with_tuple_config():
    """测试OCRResult保存包含tuple的配置"""
    print("\n🔍 测试OCRResult保存包含tuple的配置...")
    
    try:
        from core.base_ocr import BaseOCR, OCRResult
        
        # 创建包含各种数据类型的配置
        test_config = {
            "string_value": "test_string",
            "int_value": 42,
            "bool_value": True,
            "none_value": None,
            "list_value": ["item1", "item2", "item3"],
            "tuple_value": ("tuple1", "tuple2", "tuple3"),
            "mixed_list": [1, "two", 3.0, True],
            "nested_dict": {"key1": "value1", "key2": ["nested", "list"]},
            "langs": ["en", "zh", "fr"]  # 这是最常见的问题源
        }
        
        # 创建测试OCR实例
        class TestOCR(BaseOCR):
            def process_document(self, pdf_path: str, **kwargs) -> OCRResult:
                pass
            def validate_config(self) -> bool:
                return True
        
        ocr = TestOCR(test_config)
        
        # 创建测试结果
        result = OCRResult(
            model_name="Test Model",
            success=True,
            error=None,
            processing_time=10.5,
            text_length=100,
            text_content="测试文本内容",
            output_folder=None,
            config=test_config,
            attempts=1
        )
        
        # 使用临时目录测试保存
        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                output_folder = ocr.save_output(result, temp_dir)
                print(f"✅ 输出保存成功: {output_folder}")
                
                # 检查配置文件是否正确创建
                config_file = Path(output_folder) / "config.txt"
                if config_file.exists():
                    print("✅ 配置文件创建成功")
                    
                    # 读取并检查内容
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        print("✅ 配置文件内容读取成功")
                        
                        # 检查是否包含预期的转换结果
                        if "list_value: item1, item2, item3" in content:
                            print("✅ list值正确转换")
                        if "tuple_value: tuple1, tuple2, tuple3" in content:
                            print("✅ tuple值正确转换")
                        if "langs: en, zh, fr" in content:
                            print("✅ langs值正确转换")
                        
                        print(f"\n📄 配置文件内容预览:")
                        print("-" * 40)
                        print(content[:500] + "..." if len(content) > 500 else content)
                        print("-" * 40)
                        
                else:
                    print("❌ 配置文件未创建")
                    return False
                
                return True
                
            except Exception as e:
                print(f"❌ 保存过程中出错: {e}")
                import traceback
                traceback.print_exc()
                return False
        
    except Exception as e:
        print(f"❌ OCRResult测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ocr_factory_with_real_config():
    """测试OCR工厂使用真实配置"""
    print("\n🔍 测试OCR工厂使用真实配置...")
    
    try:
        from core import OCRFactory
        from config import LLMConfigManager
        
        config_manager = LLMConfigManager()
        if not config_manager.validate_config():
            print("⚠️ 配置无效，跳过真实配置测试")
            return True
        
        factory = OCRFactory()
        
        # 尝试创建OCR实例（不实际运行）
        try:
            if config_manager.should_use_gemini():
                ocr_instance = factory.create_marker_ocr(proxy_type="http")
                print("✅ HTTP代理OCR实例创建成功")
                
                # 检查配置是否包含正确转换的值
                config = config_manager.get_active_config()
                if 'langs' in config:
                    print(f"✅ 配置中langs类型: {type(config['langs'])}")
                    print(f"✅ 配置中langs值: {config['langs']}")
            
            return True
            
        except Exception as e:
            print(f"⚠️ OCR实例创建失败（可能是依赖问题）: {e}")
            return True  # 这不是我们要测试的错误
        
    except Exception as e:
        print(f"❌ OCR工厂测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 Tuple写入文件错误修复测试")
    print("=" * 60)
    
    tests = [
        test_config_to_dict,
        test_ocr_result_with_tuple_config,
        test_ocr_factory_with_real_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Tuple写入文件错误已修复")
        print("\n💡 修复内容:")
        print("  1. 配置类to_dict()方法现在将list/tuple转换为逗号分隔字符串")
        print("  2. save_output()方法增强了类型转换处理")
        print("  3. 添加了异常处理确保文件写入不会失败")
        return True
    else:
        print("⚠️ 部分测试失败，可能还需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
