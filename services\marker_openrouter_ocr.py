"""
Marker OCR OpenRouter实现

使用OpenRouter服务的Marker OCR（不需要代理）
"""

import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.config.parser import ConfigParser
from marker.output import text_from_rendered

from core.base_ocr import BaseOCR, OCRResult


class MarkerOpenRouterOCR(BaseOCR):
    """Marker OCR OpenRouter实现"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model_name = "OpenRouter Deepseek R1"
    
    def validate_config(self) -> bool:
        """验证OpenRouter配置"""
        required_keys = ['openai_api_key', 'openai_base_url', 'llm_service']
        return all(key in self.config and self.config[key] for key in required_keys)
    
    def process_document(self, pdf_path: str, max_retries: int = 3, **kwargs) -> OCRResult:
        """
        使用OpenRouter处理PDF文档（无需代理）
        
        Args:
            pdf_path: PDF文件路径
            max_retries: 最大重试次数
            **kwargs: 其他参数
            
        Returns:
            OCR处理结果
        """
        print(f"\n{'='*50}")
        print(f"开始 {self.model_name} OCR处理")
        print(f"文件: {pdf_path}")
        print(f"{'='*50}")
        
        if not self.validate_config():
            return OCRResult(
                model_name=self.model_name,
                success=False,
                error="配置验证失败",
                processing_time=0,
                text_length=0,
                text_content="",
                output_folder=None,
                config=self.config,
                attempts=0
            )
        
        # OpenRouter不需要代理，清除代理设置
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
        for var in proxy_vars:
            if var in os.environ:
                del os.environ[var]
        print("🔧 已清除代理设置（OpenRouter不需要代理）")
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    print(f"\n第 {attempt + 1} 次尝试...")
                
                start_time = time.time()
                
                # 创建配置解析器和转换器
                config_parser = ConfigParser(self.config)
                converter = PdfConverter(
                    config=config_parser.generate_config_dict(),
                    artifact_dict=create_model_dict(),
                    processor_list=config_parser.get_processors(),
                    renderer=config_parser.get_renderer(),
                    llm_service=config_parser.get_llm_service()
                )
                
                print("🔄 开始PDF转换...")
                
                # 执行转换
                rendered = converter(pdf_path)
                # 使用官方文档推荐的正确模式：text, _, images = text_from_rendered(rendered)
                result_from_rendered = text_from_rendered(rendered)

                # 安全地解包结果
                if isinstance(result_from_rendered, tuple) and len(result_from_rendered) >= 3:
                    text_content, metadata, extracted_images = result_from_rendered
                elif isinstance(result_from_rendered, tuple) and len(result_from_rendered) == 1:
                    text_content = result_from_rendered[0]
                    extracted_images = None
                else:
                    text_content = str(result_from_rendered)
                    extracted_images = None

                processing_time = time.time() - start_time

                print(f"✅ 转换完成！")
                print(f"⏱️ 处理时间: {processing_time:.2f}秒")
                print(f"📄 提取文本长度: {len(text_content) if isinstance(text_content, str) else 'N/A'}字符")
                
                # 安全地处理images数据
                safe_images = {}
                image_count = 0

                # 处理从text_from_rendered返回的images
                if extracted_images:
                    image_count = len(extracted_images)
                    print(f"🖼️ 提取图像数量: {image_count}")
                    # 创建安全的images字典，避免tuple等复杂类型
                    for key, value in extracted_images.items():
                        try:
                            # 只保存基本信息，避免复杂对象
                            if hasattr(value, 'size'):
                                safe_images[str(key)] = f"PIL_Image_{value.size}"
                            else:
                                safe_images[str(key)] = str(type(value).__name__)
                        except Exception as e:
                            safe_images[str(key)] = f"Error_{e}"

                # 也检查rendered对象中的images（作为备用）
                if hasattr(rendered, 'images') and rendered.images and not extracted_images:
                    image_count = len(rendered.images)
                    print(f"🖼️ 从rendered对象提取图像数量: {image_count}")
                    for key, value in rendered.images.items():
                        try:
                            if hasattr(value, 'size'):
                                safe_images[str(key)] = f"PIL_Image_{value.size}"
                            else:
                                safe_images[str(key)] = str(type(value).__name__)
                        except Exception as e:
                            safe_images[str(key)] = f"Error_{e}"

                # 保存输出
                output_folder = self.save_output(OCRResult(
                    model_name=self.model_name,
                    success=True,
                    error=None,
                    processing_time=processing_time,
                    text_length=len(text_content),
                    text_content=text_content,
                    output_folder=None,
                    config=self.config,
                    attempts=attempt + 1,
                    image_count=image_count,
                    images=safe_images
                ), ".")
                
                return OCRResult(
                    model_name=self.model_name,
                    success=True,
                    error=None,
                    processing_time=processing_time,
                    text_length=len(text_content),
                    text_content=text_content,
                    output_folder=output_folder,
                    config=self.config,
                    attempts=attempt + 1,
                    image_count=image_count,
                    images=safe_images
                )
                
            except Exception as e:
                error_msg = str(e)
                print(f"❌ 第 {attempt + 1} 次尝试失败: {error_msg}")
                
                if attempt == max_retries - 1:
                    # 最后一次尝试失败
                    return OCRResult(
                        model_name=self.model_name,
                        success=False,
                        error=error_msg,
                        processing_time=0,
                        text_length=0,
                        text_content="",
                        output_folder=None,
                        config=self.config,
                        attempts=attempt + 1
                    )
                
                # 等待后重试
                time.sleep(2)
        
        # 不应该到达这里
        return OCRResult(
            model_name=self.model_name,
            success=False,
            error="未知错误",
            processing_time=0,
            text_length=0,
            text_content="",
            output_folder=None,
            config=self.config,
            attempts=max_retries
        )
