#!/usr/bin/env python3
"""
快速测试tuple修复是否有效
"""

import sys
import os
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_quick_ocr_result():
    """快速测试OCRResult保存功能"""
    print("🔍 快速测试OCRResult保存功能...")
    
    try:
        from core.base_ocr import BaseOCR, OCRResult
        
        # 创建测试OCR实例
        class TestOCR(BaseOCR):
            def process_document(self, pdf_path: str, **kwargs) -> OCRResult:
                pass
            def validate_config(self) -> bool:
                return True
        
        # 创建包含tuple的配置（模拟真实情况）
        test_config = {
            "use_llm": True,
            "llm_service": "gemini",
            "gemini_api_key": "test_key",
            "gemini_model_name": "gemini-2.5-flash",
            "format_lines": True,
            "output_format": "markdown",
            "force_ocr": False,
            "batch_multiplier": 1,
            "langs": "en",  # 这应该是字符串，不是list
            "api_timeout": 600
        }
        
        ocr = TestOCR(test_config)
        
        # 创建测试结果
        result = OCRResult(
            model_name="Test Model",
            success=True,
            error=None,
            processing_time=10.5,
            text_length=100,
            text_content="测试文本内容",
            output_folder=None,
            config=test_config,
            attempts=1,
            image_count=0,
            images={}
        )
        
        # 使用临时目录测试保存
        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                output_folder = ocr.save_output(result, temp_dir)
                print(f"✅ 输出保存成功: {output_folder}")
                return True
                
            except Exception as e:
                print(f"❌ 保存过程中出错: {e}")
                import traceback
                traceback.print_exc()
                return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_creation():
    """测试配置创建"""
    print("\n🔍 测试配置创建...")
    
    try:
        from config import LLMConfigManager
        
        config_manager = LLMConfigManager()
        
        # 测试Gemini配置
        if config_manager.should_use_gemini():
            config = config_manager.get_active_config()
            print(f"✅ 活跃配置获取成功")
            print(f"  langs类型: {type(config.get('langs', 'N/A'))}")
            print(f"  langs值: {config.get('langs', 'N/A')}")
            
            # 检查是否有tuple值
            for key, value in config.items():
                if isinstance(value, (list, tuple)):
                    print(f"⚠️ 发现非字符串值: {key} = {value} (类型: {type(value)})")
                    return False
            
            print("✅ 所有配置值都是安全的类型")
            return True
        else:
            print("⚠️ 当前不使用Gemini，跳过测试")
            return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🧪 快速Tuple修复测试")
    print("=" * 50)
    
    tests = [
        test_config_creation,
        test_quick_ocr_result
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 快速测试通过！")
        return True
    else:
        print("⚠️ 快速测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
