#!/usr/bin/env python3
"""
测试OCR修复是否解决了tuple写入文件的问题
"""

import sys
import os
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_text_from_rendered_pattern():
    """测试text_from_rendered的正确使用模式"""
    print("🔍 测试text_from_rendered的正确使用模式...")
    
    try:
        # 模拟text_from_rendered返回的tuple
        def mock_text_from_rendered(rendered):
            """模拟text_from_rendered函数返回tuple"""
            return ("这是提取的文本内容", {"metadata": "some_data"}, {"image1": "PIL_Image_data"})
        
        # 测试错误的使用方式（之前的代码）
        print("❌ 错误的使用方式:")
        rendered = "mock_rendered_object"
        try:
            text_content = mock_text_from_rendered(rendered)  # 这会返回tuple
            print(f"  text_content类型: {type(text_content)}")
            print(f"  text_content值: {text_content}")
            
            # 尝试写入文件（这会失败）
            with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
                f.write(text_content)  # 这里会出错：write() argument must be str, not tuple
                
        except TypeError as e:
            print(f"  ✅ 预期的错误: {e}")
        
        # 测试正确的使用方式（修复后的代码）
        print("\n✅ 正确的使用方式:")
        text_content, _, extracted_images = mock_text_from_rendered(rendered)
        print(f"  text_content类型: {type(text_content)}")
        print(f"  text_content值: {text_content}")
        print(f"  extracted_images类型: {type(extracted_images)}")
        print(f"  extracted_images值: {extracted_images}")
        
        # 尝试写入文件（这应该成功）
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write(text_content)  # 这应该成功
            temp_file = f.name
        
        # 验证文件内容
        with open(temp_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"  ✅ 文件写入成功，内容: {content}")
        
        # 清理临时文件
        os.unlink(temp_file)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ocr_service_import():
    """测试OCR服务是否可以正常导入"""
    print("\n🔍 测试OCR服务导入...")
    
    try:
        from services.marker_socks5_ocr import MarkerSOCKS5OCR
        from services.marker_openrouter_ocr import MarkerOpenRouterOCR
        from services.marker_http_ocr import MarkerHTTPOCR
        
        print("✅ 所有OCR服务导入成功")
        
        # 测试创建实例
        test_config = {
            "gemini_api_key": "test_key",
            "llm_service": "gemini"
        }
        
        socks5_ocr = MarkerSOCKS5OCR(test_config)
        openrouter_ocr = MarkerOpenRouterOCR(test_config)
        http_ocr = MarkerHTTPOCR(test_config)
        
        print("✅ 所有OCR实例创建成功")
        return True
        
    except Exception as e:
        print(f"❌ OCR服务导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_base_ocr_save_output():
    """测试BaseOCR的save_output方法"""
    print("\n🔍 测试BaseOCR的save_output方法...")
    
    try:
        from core.base_ocr import BaseOCR, OCRResult
        
        class TestOCR(BaseOCR):
            def process_document(self, pdf_path: str, **kwargs) -> OCRResult:
                pass
            def validate_config(self) -> bool:
                return True
        
        # 创建测试配置（确保没有tuple）
        test_config = {
            "use_llm": True,
            "llm_service": "gemini",
            "gemini_api_key": "test_key",
            "format_lines": True,
            "output_format": "markdown",
            "langs": "en"  # 字符串，不是list
        }
        
        ocr = TestOCR(test_config)
        
        # 创建测试结果
        result = OCRResult(
            model_name="Test Model",
            success=True,
            error=None,
            processing_time=10.5,
            text_length=100,
            text_content="这是测试文本内容，应该可以正常写入文件。",
            output_folder=None,
            config=test_config,
            attempts=1,
            image_count=2,
            images={"image1": "PIL_Image_info", "image2": "PIL_Image_info"}
        )
        
        # 使用临时目录测试保存
        with tempfile.TemporaryDirectory() as temp_dir:
            output_folder = ocr.save_output(result, temp_dir)
            print(f"✅ 输出保存成功: {output_folder}")
            
            # 检查文件是否正确创建
            text_file = Path(output_folder) / "extracted_text.md"
            config_file = Path(output_folder) / "config.txt"
            images_file = Path(output_folder) / "images_info.txt"
            
            if text_file.exists():
                print("✅ 文本文件创建成功")
                with open(text_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "这是测试文本内容" in content:
                        print("✅ 文本内容正确")
            
            if config_file.exists():
                print("✅ 配置文件创建成功")
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "langs: en" in content:
                        print("✅ 配置内容正确")
            
            if images_file.exists():
                print("✅ 图像信息文件创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ save_output测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 OCR修复测试 - 解决tuple写入文件问题")
    print("=" * 60)
    
    tests = [
        test_text_from_rendered_pattern,
        test_ocr_service_import,
        test_base_ocr_save_output
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！OCR修复成功")
        print("\n💡 修复内容:")
        print("  1. 修正了text_from_rendered的使用模式")
        print("  2. 使用正确的tuple解包: text, _, images = text_from_rendered(rendered)")
        print("  3. 确保只有字符串数据被写入文件")
        print("  4. 统一了所有OCR服务的实现")
        return True
    else:
        print("⚠️ 部分测试失败，可能还需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
