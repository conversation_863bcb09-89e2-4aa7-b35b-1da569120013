#!/usr/bin/env python3
"""
简单测试OCR修复
"""

import sys
import os
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ocr_result_with_safe_text():
    """测试OCRResult的安全文本处理"""
    print("🔍 测试OCRResult的安全文本处理...")
    
    try:
        from core.base_ocr import BaseOCR, OCRResult
        
        class TestOCR(BaseOCR):
            def process_document(self, pdf_path: str, **kwargs) -> OCRResult:
                pass
            def validate_config(self) -> bool:
                return True
        
        # 测试配置
        test_config = {
            "use_llm": True,
            "llm_service": "gemini",
            "langs": "en"
        }
        
        ocr = TestOCR(test_config)
        
        # 测试1: 正常字符串文本
        print("\n📝 测试1: 正常字符串文本")
        result1 = OCRResult(
            model_name="Test Model",
            success=True,
            error=None,
            processing_time=10.5,
            text_length=20,
            text_content="这是正常的文本内容",
            output_folder=None,
            config=test_config,
            attempts=1
        )
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_folder = ocr.save_output(result1, temp_dir)
            print(f"✅ 正常文本保存成功: {output_folder}")
        
        # 测试2: tuple文本（模拟错误情况）
        print("\n📝 测试2: tuple文本（模拟错误情况）")
        result2 = OCRResult(
            model_name="Test Model",
            success=True,
            error=None,
            processing_time=10.5,
            text_length=20,
            text_content=("这是tuple中的文本", {"metadata": "data"}, {"images": "info"}),
            output_folder=None,
            config=test_config,
            attempts=1
        )
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_folder = ocr.save_output(result2, temp_dir)
            print(f"✅ tuple文本安全处理成功: {output_folder}")
            
            # 检查文件内容
            text_file = Path(output_folder) / "extracted_text.md"
            if text_file.exists():
                with open(text_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "这是tuple中的文本" in content:
                        print("✅ tuple文本正确提取")
                    else:
                        print(f"⚠️ 文本内容: {content[:100]}...")
        
        # 测试3: None文本
        print("\n📝 测试3: None文本")
        result3 = OCRResult(
            model_name="Test Model",
            success=True,
            error=None,
            processing_time=10.5,
            text_length=0,
            text_content=None,
            output_folder=None,
            config=test_config,
            attempts=1
        )
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_folder = ocr.save_output(result3, temp_dir)
            print(f"✅ None文本安全处理成功: {output_folder}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_text_from_rendered_simulation():
    """模拟text_from_rendered的正确使用"""
    print("\n🔍 模拟text_from_rendered的正确使用...")
    
    try:
        # 模拟marker的text_from_rendered函数
        def mock_text_from_rendered(rendered):
            return ("提取的文本内容", {"page_count": 1}, {"image1": "PIL_Image"})
        
        # 模拟rendered对象
        rendered = "mock_rendered"
        
        # 正确的使用方式
        result_from_rendered = mock_text_from_rendered(rendered)
        print(f"text_from_rendered返回: {type(result_from_rendered)} - {result_from_rendered}")
        
        # 安全解包
        if isinstance(result_from_rendered, tuple) and len(result_from_rendered) >= 3:
            text_content, metadata, extracted_images = result_from_rendered
            print(f"✅ 成功解包: text={text_content}, metadata={metadata}, images={extracted_images}")
        else:
            print(f"❌ 解包失败，非预期格式")
            return False
        
        # 验证text_content是字符串
        if isinstance(text_content, str):
            print(f"✅ text_content是字符串: {text_content}")
            return True
        else:
            print(f"❌ text_content不是字符串: {type(text_content)}")
            return False
        
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🧪 简单OCR修复测试")
    print("=" * 50)
    
    tests = [
        test_text_from_rendered_simulation,
        test_ocr_result_with_safe_text
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 简单测试通过！")
        print("\n💡 修复要点:")
        print("  1. text_from_rendered返回tuple: (text, metadata, images)")
        print("  2. 需要正确解包: text, _, images = text_from_rendered(rendered)")
        print("  3. save_output方法现在可以安全处理tuple类型的text_content")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
