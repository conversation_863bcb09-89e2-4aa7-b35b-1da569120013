# ===== 新架构的main.py =====
# 使用重构后的OCR架构，默认HTTP代理+Gemini配置

import asyncio
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

# 新架构导入
from core import OCRFactory
from config import LLMConfigManager, AzureConfigManager

# 向后兼容导入（可选）
# from markerpdf.legacy_functions import save_results_to_markdown


def save_results_to_markdown(results: List[Dict[str, Any]]) -> str:
    """保存结果到Markdown文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"ocr_comparison_results/comparison_report_{timestamp}.md"

    # 确保目录存在
    Path(report_file).parent.mkdir(parents=True, exist_ok=True)

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# OCR模型比较报告（新架构）\n\n")
        f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**架构版本**: 2.0（重构版本）\n\n")

        # 写入总结
        f.write("## 测试总结\n\n")
        f.write("| 模型 | 状态 | 处理时间(秒) | 文本长度 | 尝试次数 |\n")
        f.write("|------|------|-------------|----------|----------|\n")

        for result in results:
            status = "✅ 成功" if result['success'] else "❌ 失败"
            f.write(f"| {result['model_name']} | {status} | {result['processing_time']:.2f} | {result['text_length']} | {result['attempts']} |\n")

        f.write("\n")

        # 写入详细结果
        for i, result in enumerate(results, 1):
            f.write(f"## {i}. {result['model_name']}\n\n")

            if result['success']:
                f.write(f"**状态**: ✅ 成功\n")
                f.write(f"**处理时间**: {result['processing_time']:.2f}秒\n")
                f.write(f"**文本长度**: {result['text_length']}字符\n")
                f.write(f"**尝试次数**: {result['attempts']}\n")
                f.write(f"**输出目录**: {result['output_folder']}\n\n")
            else:
                f.write(f"**状态**: ❌ 失败\n")
                f.write(f"**错误信息**: {result['error']}\n")
                f.write(f"**尝试次数**: {result['attempts']}\n\n")

    return report_file


def main():
    """主函数：执行OCR模型比较测试（新架构）"""
    print("=" * 60)
    print("🚀 启动OCR模型比较测试（新架构 v2.0）")
    print("📋 测试模型: Gemini 2.5 Flash (HTTP代理) vs OpenRouter Deepseek vs Azure Document AI")
    print("🔧 使用增强的代理管理机制和统一OCR接口")
    print("=" * 60)

    results = []

    # 创建OCR工厂
    factory = OCRFactory()

    # 验证配置
    config_status = factory.validate_configurations()
    print(f"\n📋 配置验证:")
    print(f"  LLM配置: {'✅' if config_status['llm_config'] else '❌'}")
    print(f"  Azure配置: {'✅' if config_status['azure_config'] else '❌'}")

    # 测试Gemini模型（默认HTTP代理）
    print(f"\n{'='*50}")
    print("🔍 测试Gemini模型（HTTP代理，推荐配置）")
    print(f"{'='*50}")
    try:
        gemini_ocr = factory.create_marker_ocr(proxy_type="http")  # 默认HTTP代理
        gemini_result = gemini_ocr.process_document("sample.pdf")
        results.append({
            'model_name': gemini_result.model_name,
            'success': gemini_result.success,
            'error': gemini_result.error,
            'processing_time': gemini_result.processing_time,
            'text_length': gemini_result.text_length,
            'text_content': gemini_result.text_content,
            'output_folder': gemini_result.output_folder,
            'attempts': gemini_result.attempts
        })
    except Exception as e:
        print(f"❌ Gemini测试失败: {e}")
        results.append({
            'model_name': 'Gemini 2.5 Flash (HTTP Proxy)',
            'success': False,
            'error': str(e),
            'processing_time': 0,
            'text_length': 0,
            'text_content': '',
            'output_folder': None,
            'attempts': 1
        })

    # 测试OpenRouter Deepseek模型
    print(f"\n{'='*50}")
    print("🔍 测试OpenRouter Deepseek模型")
    print(f"{'='*50}")
    try:
        # 临时切换到OpenRouter配置
        llm_config = LLMConfigManager()
        original_use_gemini = llm_config.should_use_gemini()

        # 创建OpenRouter OCR
        openrouter_ocr = factory.create_marker_ocr(proxy_type="socks5")  # OpenRouter不需要代理
        openrouter_result = openrouter_ocr.process_document("sample.pdf")
        results.append({
            'model_name': openrouter_result.model_name,
            'success': openrouter_result.success,
            'error': openrouter_result.error,
            'processing_time': openrouter_result.processing_time,
            'text_length': openrouter_result.text_length,
            'text_content': openrouter_result.text_content,
            'output_folder': openrouter_result.output_folder,
            'attempts': openrouter_result.attempts
        })
    except Exception as e:
        print(f"❌ OpenRouter测试失败: {e}")
        results.append({
            'model_name': 'OpenRouter Deepseek R1',
            'success': False,
            'error': str(e),
            'processing_time': 0,
            'text_length': 0,
            'text_content': '',
            'output_folder': None,
            'attempts': 1
        })

    # 测试Azure Document AI模型
    print(f"\n{'='*50}")
    print("🔍 测试Azure Document AI模型")
    print(f"{'='*50}")
    try:
        azure_ocr = factory.create_azure_ocr()
        if azure_ocr:
            azure_result = azure_ocr.process_document("sample.pdf")
            results.append({
                'model_name': azure_result.model_name,
                'success': azure_result.success,
                'error': azure_result.error,
                'processing_time': azure_result.processing_time,
                'text_length': azure_result.text_length,
                'text_content': azure_result.text_content,
                'output_folder': azure_result.output_folder,
                'attempts': azure_result.attempts
            })
        else:
            print("❌ Azure配置无效")
            results.append({
                'model_name': 'Azure Document AI',
                'success': False,
                'error': 'Azure配置无效',
                'processing_time': 0,
                'text_length': 0,
                'text_content': '',
                'output_folder': None,
                'attempts': 0
            })
    except Exception as e:
        print(f"❌ Azure测试失败: {e}")
        results.append({
            'model_name': 'Azure Document AI',
            'success': False,
            'error': str(e),
            'processing_time': 0,
            'text_length': 0,
            'text_content': '',
            'output_folder': None,
            'attempts': 1
        })

    # 清理代理设置
    factory.cleanup_proxy()

    # 保存结果到Markdown文件
    report_file = save_results_to_markdown(results)

    print(f"\n{'='*60}")
    print("🎉 测试完成！")
    print(f"📊 详细比较报告: {report_file}")
    print("🏗️ 使用新架构 v2.0 - 统一OCR接口 + 依赖注入")
    print(f"{'='*60}")


if __name__ == "__main__":
    main()