#!/usr/bin/env python3
"""
测试新架构的导入和基本功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    
    try:
        import os
        print("✅ os导入成功")
        
        from dotenv import load_dotenv
        print("✅ dotenv导入成功")
        
        from typing import Dict, Any
        print("✅ typing导入成功")
        
        from dataclasses import dataclass
        print("✅ dataclasses导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 基本导入失败: {e}")
        return False

def test_config_imports():
    """测试配置模块导入"""
    print("\n🔍 测试配置模块导入...")
    
    try:
        from config.llm_config import LLMConfigManager, GeminiConfig, OpenRouterConfig
        print("✅ LLM配置类导入成功")
        
        from config.azure_config import AzureConfig, AzureConfigManager
        print("✅ Azure配置类导入成功")
        
        from config import LLMConfigManager as ConfigManager
        print("✅ 配置模块导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 配置模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_proxy_imports():
    """测试代理模块导入"""
    print("\n🔍 测试代理模块导入...")
    
    try:
        from proxy.proxy_manager import ProxyManager
        print("✅ ProxyManager导入成功")
        
        from proxy import ProxyManager as PM
        print("✅ 代理模块导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 代理模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_core_imports():
    """测试核心模块导入"""
    print("\n🔍 测试核心模块导入...")
    
    try:
        from core.base_ocr import BaseOCR, OCRResult
        print("✅ 基础OCR类导入成功")
        
        # 暂时跳过OCRFactory，因为它可能有循环导入
        # from core.ocr_factory import OCRFactory
        # print("✅ OCR工厂导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 核心模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_functionality():
    """测试配置功能"""
    print("\n🔍 测试配置功能...")
    
    try:
        from config import LLMConfigManager
        
        config_manager = LLMConfigManager()
        print("✅ LLMConfigManager实例化成功")
        
        use_gemini = config_manager.should_use_gemini()
        print(f"✅ 使用Gemini: {use_gemini}")
        
        if use_gemini:
            gemini_config = config_manager.create_gemini_config()
            print(f"✅ Gemini配置创建成功: {gemini_config.gemini_model_name}")
        else:
            openrouter_config = config_manager.create_openrouter_config()
            print(f"✅ OpenRouter配置创建成功: {openrouter_config.openai_model}")
        
        return True
    except Exception as e:
        print(f"❌ 配置功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 新架构导入和功能测试")
    print("=" * 60)
    
    tests = [
        test_basic_imports,
        test_config_imports,
        test_proxy_imports,
        test_core_imports,
        test_config_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！新架构导入正常")
        return True
    else:
        print("⚠️ 部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
