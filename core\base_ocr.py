"""
OCR基础类和接口定义

定义统一的OCR接口，支持不同的OCR实现
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path


@dataclass
class OCRResult:
    """OCR处理结果"""
    model_name: str
    success: bool
    error: Optional[str]
    processing_time: float
    text_length: int
    text_content: str
    output_folder: Optional[str]
    config: Dict[str, Any]
    attempts: int
    image_count: int = 0
    images: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.images is None:
            self.images = {}


class BaseOCR(ABC):
    """OCR基础抽象类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化OCR实例
        
        Args:
            config: OCR配置字典
        """
        self.config = config
        self.model_name = "Unknown OCR"
    
    @abstractmethod
    def process_document(self, pdf_path: str, **kwargs) -> OCRResult:
        """
        处理文档的抽象方法
        
        Args:
            pdf_path: PDF文件路径
            **kwargs: 其他参数
            
        Returns:
            OCR处理结果
        """
        pass
    
    @abstractmethod
    def validate_config(self) -> bool:
        """
        验证配置是否有效
        
        Returns:
            配置是否有效
        """
        pass
    
    def save_output(self, result: OCRResult, output_dir: str) -> str:
        """
        保存OCR输出结果
        
        Args:
            result: OCR结果
            output_dir: 输出目录
            
        Returns:
            输出文件夹路径
        """
        from datetime import datetime
        
        # 创建输出目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        folder_name = f"output_{result.model_name.replace(' ', '_')}_{timestamp}"
        output_folder = Path(output_dir) / folder_name
        output_folder.mkdir(parents=True, exist_ok=True)
        
        # 保存文本内容
        text_file = output_folder / "extracted_text.md"
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(f"# OCR结果 - {result.model_name}\n\n")
            f.write(f"**处理时间**: {result.processing_time:.2f}秒\n")
            f.write(f"**文本长度**: {result.text_length}字符\n")
            f.write(f"**尝试次数**: {result.attempts}\n\n")
            f.write("## 提取的文本内容\n\n")

            # 安全地处理text_content，确保它是字符串
            try:
                if isinstance(result.text_content, (tuple, list)):
                    # 如果text_content是tuple或list，提取第一个元素（应该是文本）
                    if len(result.text_content) > 0:
                        text_to_write = str(result.text_content[0])
                    else:
                        text_to_write = "无文本内容"
                    print(f"⚠️ text_content是{type(result.text_content).__name__}，已提取文本部分")
                elif result.text_content is None:
                    text_to_write = "无文本内容"
                else:
                    text_to_write = str(result.text_content)

                f.write(text_to_write)
            except Exception as e:
                error_msg = f"文本内容写入失败: {e}\n类型: {type(result.text_content)}\n内容: {result.text_content}"
                print(f"❌ {error_msg}")
                f.write(f"错误: {error_msg}")
        
        # 保存配置信息
        config_file = output_folder / "config.txt"
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(f"OCR模型配置 - {result.model_name}\n")
            f.write("=" * 50 + "\n\n")
            for key, value in result.config.items():
                # 确保value是字符串格式，处理各种数据类型
                try:
                    if isinstance(value, (list, tuple)):
                        # 对于列表和元组，转换为逗号分隔的字符串
                        if all(isinstance(item, str) for item in value):
                            value_str = ', '.join(value)
                        else:
                            value_str = ', '.join(str(item) for item in value)
                    elif isinstance(value, dict):
                        # 对于字典，转换为简单的键值对表示
                        value_str = str(value)
                    elif isinstance(value, bool):
                        # 布尔值转换为字符串
                        value_str = str(value)
                    elif value is None:
                        value_str = "None"
                    else:
                        value_str = str(value)

                    f.write(f"{key}: {value_str}\n")
                except Exception as e:
                    # 如果转换失败，记录错误并使用安全的默认值
                    print(f"⚠️ 配置项 {key} 转换失败: {e}")
                    f.write(f"{key}: <转换失败: {type(value).__name__}>\n")
        
        # 保存图像信息（如果有）
        if result.images and len(result.images) > 0:
            images_file = output_folder / "images_info.txt"
            with open(images_file, 'w', encoding='utf-8') as f:
                f.write(f"图像信息 - {result.model_name}\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"图像数量: {result.image_count}\n\n")

                for key, value in result.images.items():
                    try:
                        # 安全地处理图像信息
                        if hasattr(value, 'size'):
                            # PIL Image对象
                            f.write(f"{key}: PIL Image {value.size}\n")
                        elif isinstance(value, (list, tuple)):
                            f.write(f"{key}: {type(value).__name__} with {len(value)} items\n")
                        elif isinstance(value, dict):
                            f.write(f"{key}: Dictionary with {len(value)} keys\n")
                        else:
                            f.write(f"{key}: {type(value).__name__}\n")
                    except Exception as e:
                        f.write(f"{key}: <无法处理: {e}>\n")

        print(f"✅ 输出已保存到: {output_folder}")
        return str(output_folder)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            "model_name": self.model_name,
            "config": self.config
        }
