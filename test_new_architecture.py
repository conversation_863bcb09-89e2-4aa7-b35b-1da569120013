#!/usr/bin/env python3
"""
测试新架构的基本功能（快速测试）
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ocr_factory():
    """测试OCR工厂功能"""
    print("🔍 测试OCR工厂功能...")
    
    try:
        from core import OCRFactory
        from config import LLMConfigManager
        
        # 创建工厂
        factory = OCRFactory()
        print("✅ OCR工厂创建成功")
        
        # 验证配置
        config_status = factory.validate_configurations()
        print(f"✅ 配置验证: LLM={config_status['llm_config']}, Azure={config_status['azure_config']}")
        
        # 测试配置管理
        llm_config = LLMConfigManager()
        use_gemini = llm_config.should_use_gemini()
        print(f"✅ 使用Gemini: {use_gemini}")
        
        if use_gemini:
            gemini_config = llm_config.create_gemini_config()
            print(f"✅ Gemini配置: {gemini_config.gemini_model_name}")
        
        return True
    except Exception as e:
        print(f"❌ OCR工厂测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ocr_result():
    """测试OCR结果类"""
    print("\n🔍 测试OCR结果类...")
    
    try:
        from core.base_ocr import OCRResult
        
        # 创建测试结果
        result = OCRResult(
            model_name="Test Model",
            success=True,
            error=None,
            processing_time=10.5,
            text_length=100,
            text_content="测试文本内容",
            output_folder=None,
            config={"test_key": ["test", "value"], "another_key": "simple_value"},
            attempts=1
        )
        
        print("✅ OCR结果对象创建成功")
        print(f"✅ 模型名称: {result.model_name}")
        print(f"✅ 处理时间: {result.processing_time}秒")
        print(f"✅ 文本长度: {result.text_length}")
        
        return True
    except Exception as e:
        print(f"❌ OCR结果测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_save_output():
    """测试保存输出功能"""
    print("\n🔍 测试保存输出功能...")
    
    try:
        from core.base_ocr import BaseOCR, OCRResult
        from pathlib import Path
        import tempfile
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试OCR实例
            class TestOCR(BaseOCR):
                def process_document(self, pdf_path: str, **kwargs) -> OCRResult:
                    pass
                def validate_config(self) -> bool:
                    return True
            
            ocr = TestOCR({"test": "config"})
            
            # 创建测试结果（包含可能导致错误的tuple值）
            result = OCRResult(
                model_name="Test Model",
                success=True,
                error=None,
                processing_time=10.5,
                text_length=100,
                text_content="测试文本内容\n这是第二行",
                output_folder=None,
                config={
                    "simple_key": "simple_value",
                    "list_key": ["item1", "item2"],
                    "tuple_key": ("tuple", "value"),  # 这个可能导致之前的错误
                    "number_key": 42
                },
                attempts=1
            )
            
            # 测试保存功能
            output_folder = ocr.save_output(result, temp_dir)
            print(f"✅ 输出保存成功: {output_folder}")
            
            # 验证文件是否创建
            output_path = Path(output_folder)
            if output_path.exists():
                print("✅ 输出目录创建成功")
                
                text_file = output_path / "extracted_text.md"
                config_file = output_path / "config.txt"
                
                if text_file.exists():
                    print("✅ 文本文件创建成功")
                    with open(text_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if "测试文本内容" in content:
                            print("✅ 文本内容正确")
                
                if config_file.exists():
                    print("✅ 配置文件创建成功")
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if "tuple_key" in content:
                            print("✅ tuple值正确处理")
            
        return True
    except Exception as e:
        print(f"❌ 保存输出测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 新架构功能测试（快速版本）")
    print("=" * 60)
    
    tests = [
        test_ocr_factory,
        test_ocr_result,
        test_save_output
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！新架构功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
