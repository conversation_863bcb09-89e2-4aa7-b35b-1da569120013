#!/usr/bin/env python3
"""
最终测试OCR修复 - 验证所有修改都正确工作
"""

import sys
import os
import tempfile
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_all_ocr_services_import():
    """测试所有OCR服务是否可以正常导入"""
    print("🔍 测试所有OCR服务导入...")
    
    try:
        from services.marker_socks5_ocr import MarkerSOCKS5OCR
        from services.marker_openrouter_ocr import MarkerOpenRouterOCR
        from services.marker_http_ocr import MarkerHTTPOCR
        
        print("✅ 所有OCR服务导入成功")
        
        # 测试创建实例
        test_config = {
            "gemini_api_key": "test_key",
            "llm_service": "gemini",
            "use_llm": True,
            "langs": "en"
        }
        
        socks5_ocr = MarkerSOCKS5OCR(test_config)
        openrouter_ocr = MarkerOpenRouterOCR(test_config)
        http_ocr = MarkerHTTPOCR(test_config)
        
        print("✅ 所有OCR实例创建成功")
        print(f"  - SOCKS5 OCR: {socks5_ocr.model_name}")
        print(f"  - OpenRouter OCR: {openrouter_ocr.model_name}")
        print(f"  - HTTP OCR: {http_ocr.model_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR服务导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_text_from_rendered_fix():
    """测试text_from_rendered修复"""
    print("\n🔍 测试text_from_rendered修复...")
    
    try:
        # 模拟不同的text_from_rendered返回情况
        test_cases = [
            # 标准情况：返回3元素tuple
            ("标准3元素tuple", ("文本内容", {"metadata": "data"}, {"images": "info"})),
            # 单元素tuple
            ("单元素tuple", ("文本内容",)),
            # 直接字符串
            ("直接字符串", "文本内容"),
            # 空tuple
            ("空tuple", ()),
        ]
        
        for case_name, mock_result in test_cases:
            print(f"\n  📝 测试 {case_name}:")
            
            # 模拟我们的解包逻辑
            if isinstance(mock_result, tuple) and len(mock_result) >= 3:
                text_content, metadata, extracted_images = mock_result
                print(f"    ✅ 3元素解包: text={text_content}")
            elif isinstance(mock_result, tuple) and len(mock_result) == 1:
                text_content = mock_result[0]
                extracted_images = None
                print(f"    ✅ 单元素解包: text={text_content}")
            elif isinstance(mock_result, tuple) and len(mock_result) == 0:
                text_content = "无内容"
                extracted_images = None
                print(f"    ✅ 空tuple处理: text={text_content}")
            else:
                text_content = str(mock_result)
                extracted_images = None
                print(f"    ✅ 字符串处理: text={text_content}")
            
            # 验证text_content是字符串
            if isinstance(text_content, str):
                print(f"    ✅ 结果是字符串: {text_content}")
            else:
                print(f"    ❌ 结果不是字符串: {type(text_content)}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ text_from_rendered测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_save_output_safety():
    """测试save_output的安全性"""
    print("\n🔍 测试save_output安全性...")
    
    try:
        from core.base_ocr import BaseOCR, OCRResult
        
        class TestOCR(BaseOCR):
            def process_document(self, pdf_path: str, **kwargs) -> OCRResult:
                pass
            def validate_config(self) -> bool:
                return True
        
        test_config = {"test": "config"}
        ocr = TestOCR(test_config)
        
        # 测试不同类型的text_content
        test_cases = [
            ("正常字符串", "这是正常的文本内容"),
            ("tuple文本", ("这是tuple中的文本", "metadata", "images")),
            ("None文本", None),
            ("空字符串", ""),
            ("数字", 12345),
        ]
        
        for case_name, text_content in test_cases:
            print(f"\n  📝 测试 {case_name}:")
            
            result = OCRResult(
                model_name="Test Model",
                success=True,
                error=None,
                processing_time=1.0,
                text_length=len(str(text_content)) if text_content else 0,
                text_content=text_content,
                output_folder=None,
                config=test_config,
                attempts=1
            )
            
            with tempfile.TemporaryDirectory() as temp_dir:
                try:
                    output_folder = ocr.save_output(result, temp_dir)
                    print(f"    ✅ 保存成功: {Path(output_folder).name}")
                    
                    # 检查文件是否创建
                    text_file = Path(output_folder) / "extracted_text.md"
                    if text_file.exists():
                        print(f"    ✅ 文本文件创建成功")
                    else:
                        print(f"    ❌ 文本文件未创建")
                        return False
                        
                except Exception as e:
                    print(f"    ❌ 保存失败: {e}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ save_output测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_safety():
    """测试配置安全性"""
    print("\n🔍 测试配置安全性...")
    
    try:
        from core.base_ocr import BaseOCR, OCRResult
        
        class TestOCR(BaseOCR):
            def process_document(self, pdf_path: str, **kwargs) -> OCRResult:
                pass
            def validate_config(self) -> bool:
                return True
        
        # 创建包含各种数据类型的配置
        test_config = {
            "string_value": "test_string",
            "int_value": 42,
            "bool_value": True,
            "none_value": None,
            "list_value": ["item1", "item2", "item3"],
            "tuple_value": ("tuple1", "tuple2", "tuple3"),
            "dict_value": {"nested": "dict"},
            "langs": "en"  # 确保是字符串
        }
        
        ocr = TestOCR(test_config)
        
        result = OCRResult(
            model_name="Test Model",
            success=True,
            error=None,
            processing_time=1.0,
            text_length=10,
            text_content="测试文本",
            output_folder=None,
            config=test_config,
            attempts=1
        )
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_folder = ocr.save_output(result, temp_dir)
            print(f"✅ 复杂配置保存成功: {Path(output_folder).name}")
            
            # 检查配置文件
            config_file = Path(output_folder) / "config.txt"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print("✅ 配置文件创建成功")
                    
                    # 检查关键转换
                    if "list_value: item1, item2, item3" in content:
                        print("✅ list正确转换")
                    if "tuple_value: tuple1, tuple2, tuple3" in content:
                        print("✅ tuple正确转换")
                    if "langs: en" in content:
                        print("✅ langs正确保存")
            else:
                print("❌ 配置文件未创建")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 最终OCR修复测试")
    print("=" * 60)
    print("验证所有修改都正确工作，解决 'write() argument must be str, not tuple' 错误")
    print("=" * 60)
    
    tests = [
        test_all_ocr_services_import,
        test_text_from_rendered_fix,
        test_save_output_safety,
        test_config_safety
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 最终测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！OCR修复完全成功")
        print("\n💡 修复总结:")
        print("  1. ✅ 修正了text_from_rendered的使用模式")
        print("  2. ✅ 使用正确的tuple解包处理")
        print("  3. ✅ 增强了save_output的安全性")
        print("  4. ✅ 统一了所有OCR服务的实现")
        print("  5. ✅ 添加了配置类型转换保护")
        print("\n🔧 关键修改:")
        print("  - text_from_rendered(rendered) → 安全解包tuple")
        print("  - save_output现在可以处理任何类型的text_content")
        print("  - 配置写入时自动转换list/tuple为字符串")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
