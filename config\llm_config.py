"""
LLM服务配置管理

统一管理Gemini和OpenRouter的配置，支持从环境变量读取设置
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv


@dataclass
class GeminiConfig:
    """Gemini模型配置"""
    use_llm: bool
    llm_service: str
    gemini_api_key: str
    gemini_model_name: str
    format_lines: bool
    output_format: str
    force_ocr: bool
    batch_multiplier: int
    langs: list
    api_timeout: int = 600  # 默认10分钟超时
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，确保所有值都是可序列化的"""
        return {
            "use_llm": self.use_llm,
            "llm_service": self.llm_service,
            "gemini_api_key": self.gemini_api_key,
            "gemini_model_name": self.gemini_model_name,
            "format_lines": self.format_lines,
            "output_format": self.output_format,
            "force_ocr": self.force_ocr,
            "batch_multiplier": self.batch_multiplier,
            "langs": ','.join(self.langs) if isinstance(self.langs, (list, tuple)) else str(self.langs),
            "api_timeout": self.api_timeout
        }


@dataclass
class OpenRouterConfig:
    """OpenRouter模型配置"""
    use_llm: bool
    llm_service: str
    openai_api_key: str
    openai_base_url: str
    openai_model: str
    format_lines: bool
    output_format: str
    force_ocr: bool
    batch_multiplier: int
    langs: list
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，确保所有值都是可序列化的"""
        return {
            "use_llm": self.use_llm,
            "llm_service": self.llm_service,
            "openai_api_key": self.openai_api_key,
            "openai_base_url": self.openai_base_url,
            "openai_model": self.openai_model,
            "format_lines": self.format_lines,
            "output_format": self.output_format,
            "force_ocr": self.force_ocr,
            "batch_multiplier": self.batch_multiplier,
            "langs": ','.join(self.langs) if isinstance(self.langs, (list, tuple)) else str(self.langs)
        }


class LLMConfigManager:
    """LLM配置管理器"""
    
    def __init__(self, env_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            env_file: 环境变量文件路径，默认为None（使用默认.env文件）
        """
        if env_file:
            load_dotenv(env_file)
        else:
            load_dotenv()
    
    def should_use_gemini(self) -> bool:
        """检查是否应该使用Gemini服务"""
        return os.getenv('USE_GEMINI', 'true').lower() == 'true'
    
    def create_gemini_config(self) -> GeminiConfig:
        """创建Gemini配置"""
        return GeminiConfig(
            use_llm=os.getenv('USE_LLM', 'true').lower() == 'true',
            llm_service=os.getenv('MARKER_GEMINI_SERIVES', ''),
            gemini_api_key=os.getenv('GEMINI_API_KEY', ''),
            gemini_model_name=os.getenv('GEMINI_MODEL', ''),
            format_lines=os.getenv('MARKER_FORMAT_LINES', 'true').lower() == 'true',
            output_format=os.getenv('MARKER_OUTPUT_FORMAT', 'markdown'),
            force_ocr=os.getenv('MARKER_FORCE_OCR', 'false').lower() == 'true',
            batch_multiplier=int(os.getenv('MARKER_BATCH_MULTIPLIER', '1')),
            langs=os.getenv('MARKER_LANG', 'en').split(','),
            api_timeout=int(os.getenv('GEMINI_API_TIMEOUT', '600'))
        )
    
    def create_openrouter_config(self) -> OpenRouterConfig:
        """创建OpenRouter配置"""
        return OpenRouterConfig(
            use_llm=os.getenv('USE_LLM', 'true').lower() == 'true',
            llm_service=os.getenv('MARKER_OPENROUTER_SERIVES', ''),
            openai_api_key=os.getenv('OPENROUTER_API_KEY', ''),
            openai_base_url=os.getenv('OPENROUTER_URL', ''),
            openai_model=os.getenv('OPENROUTER_MODEL', ''),
            format_lines=os.getenv('MARKER_FORMAT_LINES', 'true').lower() == 'true',
            output_format=os.getenv('MARKER_OUTPUT_FORMAT', 'markdown'),
            force_ocr=os.getenv('MARKER_FORCE_OCR', 'false').lower() == 'true',
            batch_multiplier=int(os.getenv('MARKER_BATCH_MULTIPLIER', '1')),
            langs=os.getenv('MARKER_LANG', 'en').split(',')
        )
    
    def get_active_config(self) -> Dict[str, Any]:
        """
        根据USE_GEMINI标志获取当前活跃的配置
        
        Returns:
            当前活跃的LLM配置字典
        """
        if self.should_use_gemini():
            config = self.create_gemini_config()
            print("🔧 使用Gemini配置")
            return config.to_dict()
        else:
            config = self.create_openrouter_config()
            print("🔧 使用OpenRouter配置")
            return config.to_dict()
    
    def get_proxy_url(self) -> Optional[str]:
        """获取代理URL（仅Gemini需要）"""
        if self.should_use_gemini():
            return os.getenv('NORDVPN_PROXY')
        return None
    
    def validate_config(self) -> bool:
        """验证当前配置是否完整"""
        if self.should_use_gemini():
            config = self.create_gemini_config()
            return bool(config.gemini_api_key and config.llm_service)
        else:
            config = self.create_openrouter_config()
            return bool(config.openai_api_key and config.openai_base_url and config.llm_service)
